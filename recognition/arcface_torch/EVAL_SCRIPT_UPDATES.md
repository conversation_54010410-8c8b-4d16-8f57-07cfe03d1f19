# eval.sh Script Updates - Integration with Optimized face_match_test.py

## Overview

The `eval.sh` script has been updated to integrate with the optimized `face_match_test.py` script, providing enhanced flexibility and new testing capabilities.

## Key Updates

### 1. Enhanced Product Case Testing (`run_product_case_test`)

**Before:**
```bash
run_product_case_test() {
    local test_img_dir="/CE_Data/member/weili/code/insightface_1022/recognition/arcface_torch/data/量产反馈问题"
    local threshold="${1:-0.5}"
    # ... hardcoded paths
}
```

**After:**
```bash
run_product_case_test() {
    local test_img_dir="${1:-/CE_Data/member/weili/code/insightface_1022/recognition/arcface_torch/data/量产反馈问题}"
    local threshold="${2:-0.5}"
    local output_dir="${MODEL_DIR}/product_test_results"
    # ... flexible parameters with automatic v1/v2 test selection
}
```

**Improvements:**
- Configurable test image directory
- Organized output directory structure
- Automatic v1/v2 test selection based on existing results
- Better error handling and validation

### 2. New Image Pair Testing (`run_image_pair_test`)

**New Function:**
```bash
run_image_pair_test() {
    local enroll_img="$1"
    local auth_img="$2"
    local threshold="${3:-0.5}"
    local output_dir="${MODEL_DIR}/pair_test_results"
    # ... implementation for single image pair testing
}
```

**Features:**
- Direct comparison between two specific images
- Configurable similarity threshold
- Separate output directory for results
- Input validation and error handling

### 3. Enhanced Command-Line Interface

**New Command-Line Options:**
- `--product-test [DIR] [THRESHOLD]`: Enhanced product case testing
- `--pair-test ENROLL AUTH [THRESHOLD]`: New image pair testing

**Usage Examples:**
```bash
# Product case testing
./eval.sh --config ms1mv3_r50 --product-test                           # Default settings
./eval.sh --config ms1mv3_r50 --product-test /path/to/images          # Custom directory
./eval.sh --config ms1mv3_r50 --product-test /path/to/images 0.6      # Custom dir + threshold
./eval.sh --config ms1mv3_r50 --product-test 0.7                      # Custom threshold only

# Image pair testing
./eval.sh --config ms1mv3_r50 --pair-test enroll.jpg auth.jpg         # Default threshold
./eval.sh --config ms1mv3_r50 --pair-test enroll.jpg auth.jpg 0.6     # Custom threshold
```

### 4. Updated Interactive Mode

**Enhanced Menu:**
```
Available Operations:
  8) Run product case test (folder) - Enhanced with flexible paths
  9) Run image pair test - New feature for single pair testing
  10) Run quantization
  ...
```

**Interactive Features:**
- Step-by-step configuration prompts for both test types
- Input validation and error checking
- Real-time feedback and progress reporting

### 5. Improved Help Documentation

**Enhanced Help Output:**
```bash
Commands:
    --product-test [DIR] [THRESHOLD]  Run product case test
    --pair-test ENROLL AUTH [THRESHOLD]  Run image pair test
    ...

Examples:
    ./eval.sh --config ms1mv3_r50 --product-test /path/to/test/images 0.6
    ./eval.sh --config ms1mv3_r50 --pair-test enroll.jpg auth.jpg 0.5
```

## Integration Benefits

### 1. Flexibility
- **Before**: Fixed paths and limited configuration options
- **After**: Fully configurable paths and parameters

### 2. Functionality
- **Before**: Only folder-based batch testing
- **After**: Both folder-based batch testing and single image pair testing

### 3. Output Organization
- **Before**: Results scattered in hardcoded directories
- **After**: Organized output structure with version-specific directories

### 4. User Experience
- **Before**: Required code modification for different test scenarios
- **After**: Command-line configuration for all scenarios

### 5. Automation
- **Before**: Manual intervention required for different test types
- **After**: Automatic test type selection and optimization

## Output Directory Structure

The updated script creates organized output directories:

```
work_dirs/{CONFIG_NAME}_{VERSION}/
├── product_test_results/          # Product case test results
│   ├── result_{VERSION}/          # Individual result images
│   ├── info_{VERSION}.json        # Detailed matching information
│   └── result_{VERSION}.txt       # Summary statistics
└── pair_test_results/             # Image pair test results
    └── imgpair_test.jpg           # Comparison result image
```

## Migration Guide

### For Existing Scripts

1. **No changes required** for basic usage - existing commands continue to work
2. **Optional enhancements** available through new parameters
3. **Gradual migration** path for adopting new features

### For New Implementations

1. Use `--product-test` with custom directories and thresholds
2. Use `--pair-test` for quick single image pair validation
3. Leverage interactive mode for complex workflows
4. Check organized output directories for results

## Performance Improvements

1. **Smart Test Selection**: Automatic v1/v2 test selection based on existing results
2. **Faster Repeated Tests**: Reuse existing inference results when available
3. **Efficient Output**: Organized directory structure reduces file system overhead
4. **Better Resource Management**: Improved error handling and cleanup

## Backward Compatibility

- All existing command-line options continue to work
- Default behavior maintained for existing workflows
- Gradual adoption path for new features
- No breaking changes to existing functionality

## Future Enhancements

The updated architecture supports easy addition of:
- Additional test types and evaluation metrics
- More flexible output formats
- Enhanced visualization options
- Integration with other evaluation tools
