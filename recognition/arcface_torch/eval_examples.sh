#!/bin/bash

# =============================================================================
# eval.sh Usage Examples - Updated for face_match_test.py optimization
# =============================================================================

echo "=== eval.sh Usage Examples ==="
echo "This script demonstrates the updated eval.sh functionality"
echo "with the optimized face_match_test.py integration"
echo ""

# =============================================================================
# Basic Configuration Examples
# =============================================================================

echo "1. Basic Evaluation (Default XM Cloud Cat Eye):"
echo "./eval.sh --config ms1mv3_r50 --version exp200 --gpu 0"
echo ""

echo "2. Interactive Mode:"
echo "./eval.sh --config ms1mv3_r50 --version exp200 --interactive"
echo ""

# =============================================================================
# Product Case Testing Examples (Updated)
# =============================================================================

echo "3. Product Case Test with Default Settings:"
echo "./eval.sh --config ms1mv3_r50 --version exp200 --product-test"
echo "   - Uses default test directory and threshold 0.5"
echo "   - Results saved to work_dirs/ms1mv3_r50_exp200/product_test_results/"
echo ""

echo "4. Product Case Test with Custom Directory:"
echo "./eval.sh --config ms1mv3_r50 --version exp200 --product-test /path/to/test/images"
echo "   - Uses custom test directory with default threshold 0.5"
echo ""

echo "5. Product Case Test with Custom Directory and Threshold:"
echo "./eval.sh --config ms1mv3_r50 --version exp200 --product-test /path/to/test/images 0.6"
echo "   - Uses custom test directory and threshold 0.6"
echo ""

echo "6. Product Case Test with Custom Threshold Only:"
echo "./eval.sh --config ms1mv3_r50 --version exp200 --product-test 0.7"
echo "   - Uses default directory with custom threshold 0.7"
echo ""

# =============================================================================
# Image Pair Testing Examples (New Feature)
# =============================================================================

echo "7. Image Pair Test with Default Threshold:"
echo "./eval.sh --config ms1mv3_r50 --version exp200 --pair-test enroll.jpg auth.jpg"
echo "   - Tests matching between two specific images"
echo "   - Uses default threshold 0.5"
echo "   - Results saved to work_dirs/ms1mv3_r50_exp200/pair_test_results/"
echo ""

echo "8. Image Pair Test with Custom Threshold:"
echo "./eval.sh --config ms1mv3_r50 --version exp200 --pair-test enroll.jpg auth.jpg 0.6"
echo "   - Tests matching with custom threshold 0.6"
echo ""

echo "9. Image Pair Test with Full Paths:"
echo "./eval.sh --config ms1mv3_r50 --version exp200 --pair-test /path/to/enroll.jpg /path/to/auth.jpg 0.5"
echo ""

# =============================================================================
# Model Conversion Examples
# =============================================================================

echo "10. ONNX Conversion (Fixed Input):"
echo "./eval.sh --config ms1mv3_r50 --version exp200 --onnx fix"
echo ""

echo "11. ONNX Conversion (Dynamic Input):"
echo "./eval.sh --config ms1mv3_r50 --version exp200 --onnx dynamic"
echo ""

echo "12. TorchScript Conversion:"
echo "./eval.sh --config ms1mv3_r50 --version exp200 --torchscript"
echo ""

# =============================================================================
# Evaluation Examples
# =============================================================================

echo "13. Run All Evaluations:"
echo "./eval.sh --config ms1mv3_r50 --version exp200 --all-eval"
echo ""

echo "14. Run All Conversions:"
echo "./eval.sh --config ms1mv3_r50 --version exp200 --all-convert"
echo ""

echo "15. Specific Dataset Evaluation:"
echo "./eval.sh --config ms1mv3_r50 --version exp200 --reco2023"
echo ""

# =============================================================================
# Advanced Examples
# =============================================================================

echo "16. GradCAM Visualization:"
echo "./eval.sh --config ms1mv3_r50 --version exp200 --gradcam /path/to/images"
echo ""

echo "17. Model Quantization:"
echo "./eval.sh --config ms1mv3_r50 --version exp200 --quantize"
echo ""

echo "18. Pruning Analysis:"
echo "./eval.sh --config ms1mv3_r50 --version exp200 --prune-analysis"
echo ""

# =============================================================================
# Key Improvements Summary
# =============================================================================

echo ""
echo "=== Key Improvements in Updated eval.sh ==="
echo ""
echo "1. Enhanced Product Case Testing:"
echo "   - Flexible input directory configuration"
echo "   - Automatic v1/v2 test selection based on existing results"
echo "   - Organized output directory structure"
echo "   - Better error handling and validation"
echo ""
echo "2. New Image Pair Testing:"
echo "   - Direct comparison between two specific images"
echo "   - Configurable similarity threshold"
echo "   - Separate output directory for pair test results"
echo ""
echo "3. Improved Parameter Handling:"
echo "   - All hardcoded paths converted to parameters"
echo "   - Better command-line argument parsing"
echo "   - Enhanced interactive mode with more options"
echo ""
echo "4. Better Output Organization:"
echo "   - Results organized by test type and version"
echo "   - Automatic directory creation"
echo "   - Comprehensive logging and status reporting"
echo ""
echo "5. Backward Compatibility:"
echo "   - Existing commands continue to work"
echo "   - Default values maintain previous behavior"
echo "   - Gradual migration path for existing workflows"
echo ""

# =============================================================================
# Interactive Mode Features
# =============================================================================

echo "=== Interactive Mode Features ==="
echo ""
echo "The interactive mode now includes:"
echo "  8) Run product case test (folder) - Enhanced with flexible paths"
echo "  9) Run image pair test - New feature for single pair testing"
echo ""
echo "Interactive mode provides:"
echo "- Step-by-step configuration prompts"
echo "- Input validation and error checking"
echo "- Real-time feedback and progress reporting"
echo "- Easy access to all evaluation and conversion features"
echo ""

echo "=== Usage Tips ==="
echo ""
echo "1. Use --interactive for first-time users or complex workflows"
echo "2. Use --product-test for batch testing of multiple image pairs"
echo "3. Use --pair-test for quick single image pair validation"
echo "4. Check output directories for detailed results and logs"
echo "5. Use existing info JSON files to speed up repeated tests"
echo ""

echo "For more information, run: ./eval.sh --help"
